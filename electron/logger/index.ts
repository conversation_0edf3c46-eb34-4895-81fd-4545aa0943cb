/**
 * Electron日志系统
 *
 * 实现日志文件持久化，解决打包后无法查看控制台日志的问题
 */

import * as path from "path";
import * as fs from "fs";
import { app } from "electron";
import electronLog from "electron-log";

// 日志级别类型
export type LogLevel = "error" | "warn" | "info" | "verbose" | "debug" | "silly";

// 日志配置接口
export interface LoggerConfig {
  // 日志文件存储路径
  logPath?: string;
  // 日志级别
  level?: LogLevel;
  // 日志文件大小限制（字节）
  maxSize?: number;
  // 保留的日志文件数量
  maxFiles?: number;
  // 是否在控制台输出
  console?: boolean;
  // 是否包含时间戳
  timestamp?: boolean;
  // 是否包含进程信息
  processInfo?: boolean;
  // 是否启用日志轮转
  rotation?: boolean;
  // 是否在日志中包含模块信息
  moduleInfo?: boolean;
}

// 默认配置
const DEFAULT_CONFIG: LoggerConfig = {
  level: "info",
  maxSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5,
  console: true,
  timestamp: true,
  processInfo: true,
  rotation: true,
  moduleInfo: true,
};

/**
 * 日志管理器类
 */
class LogManager {
  private static instance: LogManager;
  private config: LoggerConfig;
  private mainLogger: typeof electronLog;
  private initialized: boolean = false;

  private constructor() {
    this.config = { ...DEFAULT_CONFIG };
    this.mainLogger = electronLog;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LogManager {
    if (!LogManager.instance) {
      LogManager.instance = new LogManager();
    }
    return LogManager.instance;
  }

  /**
   * 初始化日志系统
   */
  public initialize(config?: LoggerConfig): void {
    if (this.initialized) {
      this.mainLogger.warn("日志系统已经初始化，忽略重复调用");
      return;
    }

    // 合并配置
    this.config = { ...DEFAULT_CONFIG, ...config };

    // 设置日志文件路径
    const logPath = this.config.logPath || this.getDefaultLogPath();
    this.ensureLogDirectory(logPath);

    // 配置主日志记录器
    this.configureLogger(this.mainLogger, logPath, "main");

    // 标记为已初始化
    this.initialized = true;

    // 记录初始化信息
    this.mainLogger.info("📝 日志系统初始化完成");
    this.mainLogger.info(`📂 日志文件路径: ${logPath}`);
    this.mainLogger.info(`🔍 日志级别: ${this.config.level}`);
    this.mainLogger.info(`📊 日志文件大小限制: ${this.config.maxSize} 字节`);
    this.mainLogger.info(`🗄️ 保留日志文件数量: ${this.config.maxFiles}`);
  }

  /**
   * 获取默认日志文件路径
   */
  private getDefaultLogPath(): string {
    let basePath: string;

    if (app) {
      // 在Electron主进程中
      basePath = app.getPath("userData");
    } else {
      // 在渲染进程或测试环境中
      basePath = path.join(process.cwd(), "logs");
    }

    return path.join(basePath, "logs");
  }

  /**
   * 确保日志目录存在
   */
  private ensureLogDirectory(logPath: string): void {
    try {
      if (!fs.existsSync(logPath)) {
        fs.mkdirSync(logPath, { recursive: true });
      }
    } catch (error) {
      console.error(`创建日志目录失败: ${error}`);
      // 回退到默认位置
      const fallbackPath = path.join(process.cwd(), "logs");
      if (!fs.existsSync(fallbackPath)) {
        fs.mkdirSync(fallbackPath, { recursive: true });
      }
    }
  }

  /**
   * 配置日志记录器
   */
  private configureLogger(logger: typeof electronLog, logPath: string, name: string): void {
    // 设置日志文件路径 (使用新的 resolvePathFn API)
    logger.transports.file.resolvePathFn = () => path.join(logPath, `${name}.log`);

    // 设置日志级别
    logger.transports.file.level = this.config.level || "info";
    logger.transports.console.level = this.config.console ? this.config.level || "info" : false;

    // 设置日志格式
    logger.transports.file.format = this.createLogFormat();

    // 设置日志文件轮转
    if (this.config.rotation) {
      logger.transports.file.maxSize = this.config.maxSize || 10 * 1024 * 1024;
      // 注意：electron-log 5.x 中 maxFiles 属性不存在，使用 archiveLogFn 来实现日志轮转
      // 这里我们依赖 electron-log 的默认轮转行为
    }
  }

  /**
   * 创建日志格式化函数
   */
  private createLogFormat(): electronLog.Format {
    return (params) => {
      const { message, data } = params;
      let result = "";
      let processedData = [...data]; // 创建数据副本以便处理

      // 添加时间戳 (防御性编程，检查 date 是否存在)
      if (this.config.timestamp && message.date) {
        result += `[${message.date.toISOString()}] `;
      }

      // 添加日志级别
      result += `[${message.level.toUpperCase()}] `;

      // 检查第一个数据项是否是模块信息对象
      let moduleInfo = "";
      if (processedData.length > 0 && typeof processedData[0] === "object" && processedData[0] !== null && "module" in processedData[0]) {
        // 提取模块信息并格式化
        const moduleObj = processedData[0] as { module: string };
        moduleInfo = `{ module: '${moduleObj.module}' }`;
        // 从处理数据中移除模块信息对象
        processedData = processedData.slice(1);
      }

      // 添加模块信息（如果有）
      if (this.config.moduleInfo && moduleInfo) {
        result += `${moduleInfo} `;
      }

      // 添加消息内容
      result += processedData.join(" ");

      // electron-log 5.x 的格式化函数应该返回数组
      return [result];
    };
  }

  /**
   * 获取主日志记录器
   */
  public getLogger(): typeof electronLog {
    if (!this.initialized) {
      this.initialize();
    }
    return this.mainLogger;
  }

  /**
   * 创建模块专用日志记录器
   */
  public createModuleLogger(moduleName: string): ModuleLogger {
    return new ModuleLogger(this.getLogger(), moduleName);
  }
}

/**
 * 模块专用日志记录器
 */
class ModuleLogger {
  private logger: typeof electronLog;
  private moduleName: string;

  constructor(logger: typeof electronLog, moduleName: string) {
    this.logger = logger;
    this.moduleName = moduleName;
  }

  error(message: string, ...args: any[]): void {
    this.logger.error({ module: this.moduleName }, message, ...args);
  }

  warn(message: string, ...args: any[]): void {
    this.logger.warn({ module: this.moduleName }, message, ...args);
  }

  info(message: string, ...args: any[]): void {
    this.logger.info({ module: this.moduleName }, message, ...args);
  }

  verbose(message: string, ...args: any[]): void {
    this.logger.verbose({ module: this.moduleName }, message, ...args);
  }

  debug(message: string, ...args: any[]): void {
    this.logger.debug({ module: this.moduleName }, message, ...args);
  }

  silly(message: string, ...args: any[]): void {
    this.logger.silly({ module: this.moduleName }, message, ...args);
  }
}

// 导出单例实例
export const logManager = LogManager.getInstance();

// 导出默认日志记录器
export const logger = logManager.getLogger();

// 导出创建模块日志记录器的函数
export function createLogger(moduleName: string): ModuleLogger {
  return logManager.createModuleLogger(moduleName);
}

// 导出初始化函数
export function initializeLogger(config?: LoggerConfig): void {
  logManager.initialize(config);
}
