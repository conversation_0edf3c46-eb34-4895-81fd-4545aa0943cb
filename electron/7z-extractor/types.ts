// 7z解压缩模块类型定义

export type ExtractionStatus = "pending" | "extracting" | "paused" | "completed" | "extract-completed" | "error" | "cancelled";

// 解压缩任务接口
export interface ExtractionTask {
  id: string;
  archivePath: string; // 压缩包路径
  extractPath: string; // 解压目标路径
  fileName: string; // 压缩包文件名
  fileSize?: number; // 压缩包大小
  extractedSize: number; // 已解压大小
  totalSize?: number; // 解压后总大小
  progress: number; // 解压进度 (0-100)
  status: ExtractionStatus;
  startTime: Date;
  endTime?: Date;
  error?: string;
  speed: number; // 解压速度 (bytes/s)
  remainingTime: number; // 剩余时间 (seconds)
  extractedFiles: number; // 已解压文件数
  totalFiles?: number; // 总文件数
  currentFile?: string; // 当前正在解压的文件
  metadata?: Record<string, any>;
  downloadTaskId?: string; // 关联的下载任务ID
  requiresPassword?: boolean; // 是否需要密码
  password?: string; // 解压密码
  deleteAfterExtraction?: boolean; // 解压完成后是否删除原文件
}

// 解压缩配置接口
export interface ExtractionConfig {
  maxConcurrent?: number; // 最大并发解压任务数，默认 2
  timeout?: number; // 超时时间，默认 5分钟
  deleteOriginalAfterExtraction?: boolean; // 解压完成后是否删除原文件，默认 false
  overwriteExisting?: boolean; // 是否覆盖已存在的文件，默认 false
  createSubfolder?: boolean; // 是否创建同名子文件夹，默认 true
  passwordPromptTimeout?: number; // 密码输入超时时间，默认 30秒
}

// 解压缩事件接口
export interface ExtractionEvents {
  "task-created": (taskId: string, task: ExtractionTask) => void;
  "task-progress": (taskId: string, progress: number, extractedSize: number, totalSize?: number) => void;
  "task-status-changed": (taskId: string, status: ExtractionStatus, error?: string) => void;
  "task-completed": (taskId: string, extractPath: string) => void;
  "task-error": (taskId: string, error: string) => void;
  "password-required": (taskId: string, callback: (password?: string) => void) => void;
  "file-conflict": (taskId: string, filePath: string, callback: (action: "overwrite" | "skip" | "rename") => void) => void;
}

// API 响应接口
export interface ExtractionApiResponse<T = any> {
  success: boolean;
  error?: string;
  taskId?: string;
  task?: ExtractionTask;
  tasks?: ExtractionTask[];
  data?: T;
}

// 存储数据接口
export interface ExtractionStoreData {
  tasks: Record<string, ExtractionTask>;
  settings: {
    maxConcurrent: number;
    timeout: number;
    deleteOriginalAfterExtraction: boolean;
    overwriteExisting: boolean;
    createSubfolder: boolean;
    passwordPromptTimeout: number;
  };
}

// 文件冲突处理选项
export type FileConflictAction = "overwrite" | "skip" | "rename";

// 解压缩进度信息
export interface ExtractionProgress {
  taskId: string;
  progress: number;
  extractedSize: number;
  totalSize?: number;
  speed: number;
  remainingTime: number;
  extractedFiles: number;
  totalFiles?: number;
  currentFile?: string;
}

// 解压缩结果
export interface ExtractionResult {
  success: boolean;
  taskId: string;
  extractPath: string;
  extractedFiles: number;
  totalSize: number;
  duration: number;
  error?: string;
}

// 7z命令输出解析结果
export interface SevenZipOutput {
  progress?: number;
  currentFile?: string;
  extractedFiles?: number;
  totalFiles?: number;
  error?: string;
  requiresPassword?: boolean;
}
