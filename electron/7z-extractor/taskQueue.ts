import { createLogger } from "../logger/index";

// 创建解压缩专用日志记录器
const extractorLogger = createLogger("extractor");
import { EventEmitter } from "events";
import type { ExtractionTask, ExtractionStatus } from "./types";

export interface QueuedTask {
  task: ExtractionTask;
  priority: number; // 优先级，数字越小优先级越高
  addedAt: Date;
}

export interface QueueStats {
  totalTasks: number;
  pendingTasks: number;
  runningTasks: number;
  completedTasks: number;
  failedTasks: number;
  maxConcurrent: number;
  currentConcurrent: number;
}

export class ExtractionTaskQueue extends EventEmitter {
  private queue: QueuedTask[] = [];
  private runningTasks: Map<string, ExtractionTask> = new Map();
  private completedTasks: Map<string, ExtractionTask> = new Map();
  private failedTasks: Map<string, ExtractionTask> = new Map();
  private maxConcurrent: number;
  private isProcessing: boolean = false;

  constructor(maxConcurrent: number = 2) {
    super();
    this.maxConcurrent = maxConcurrent;
  }

  /**
   * 添加任务到队列
   */
  addTask(task: ExtractionTask, priority: number = 0): void {
    const queuedTask: QueuedTask = {
      task,
      priority,
      addedAt: new Date(),
    };

    // 按优先级插入队列
    const insertIndex = this.queue.findIndex(item => item.priority > priority);
    if (insertIndex === -1) {
      this.queue.push(queuedTask);
    } else {
      this.queue.splice(insertIndex, 0, queuedTask);
    }

    extractorLogger.info(`📋 任务已添加到队列: ${task.fileName} (优先级: ${priority})`);
    this.emit("task-queued", task.id, task);
    
    // 尝试处理队列
    this.processQueue();
  }

  /**
   * 移除队列中的任务
   */
  removeTask(taskId: string): boolean {
    const index = this.queue.findIndex(item => item.task.id === taskId);
    if (index !== -1) {
      const removed = this.queue.splice(index, 1)[0];
      extractorLogger.info(`🗑️ 任务已从队列中移除: ${removed.task.fileName}`);
      this.emit("task-removed", taskId);
      return true;
    }
    return false;
  }

  /**
   * 获取队列中的任务
   */
  getQueuedTask(taskId: string): QueuedTask | undefined {
    return this.queue.find(item => item.task.id === taskId);
  }

  /**
   * 获取正在运行的任务
   */
  getRunningTask(taskId: string): ExtractionTask | undefined {
    return this.runningTasks.get(taskId);
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(taskId: string, status: ExtractionStatus, error?: string): void {
    // 更新队列中的任务
    const queuedTask = this.getQueuedTask(taskId);
    if (queuedTask) {
      queuedTask.task.status = status;
      if (error) {
        queuedTask.task.error = error;
      }
    }

    // 更新正在运行的任务
    const runningTask = this.runningTasks.get(taskId);
    if (runningTask) {
      runningTask.status = status;
      if (error) {
        runningTask.error = error;
      }

      // 如果任务完成或失败，移动到相应的集合
      if (status === "completed") {
        this.runningTasks.delete(taskId);
        this.completedTasks.set(taskId, runningTask);
        this.emit("task-completed", taskId, runningTask);
      } else if (status === "error" || status === "cancelled") {
        this.runningTasks.delete(taskId);
        this.failedTasks.set(taskId, runningTask);
        this.emit("task-failed", taskId, runningTask);
      }

      // 继续处理队列
      this.processQueue();
    }
  }

  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.queue.length > 0 && this.runningTasks.size < this.maxConcurrent) {
        const queuedTask = this.queue.shift();
        if (!queuedTask) break;

        const { task } = queuedTask;
        
        // 检查任务状态
        if (task.status === "cancelled") {
          continue; // 跳过已取消的任务
        }

        // 将任务移动到运行中
        this.runningTasks.set(task.id, task);
        task.status = "extracting";

        extractorLogger.info(`🚀 开始处理任务: ${task.fileName} (队列剩余: ${this.queue.length})`);
        this.emit("task-started", task.id, task);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 暂停队列处理
   */
  pauseQueue(): void {
    extractorLogger.info("⏸️ 队列处理已暂停");
    this.emit("queue-paused");
  }

  /**
   * 恢复队列处理
   */
  resumeQueue(): void {
    extractorLogger.info("▶️ 队列处理已恢复");
    this.emit("queue-resumed");
    this.processQueue();
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    const clearedCount = this.queue.length;
    this.queue = [];
    extractorLogger.info(`🧹 已清空队列，移除了 ${clearedCount} 个任务`);
    this.emit("queue-cleared", clearedCount);
  }

  /**
   * 获取队列统计信息
   */
  getStats(): QueueStats {
    return {
      totalTasks: this.queue.length + this.runningTasks.size + this.completedTasks.size + this.failedTasks.size,
      pendingTasks: this.queue.length,
      runningTasks: this.runningTasks.size,
      completedTasks: this.completedTasks.size,
      failedTasks: this.failedTasks.size,
      maxConcurrent: this.maxConcurrent,
      currentConcurrent: this.runningTasks.size,
    };
  }

  /**
   * 获取所有队列中的任务
   */
  getAllQueuedTasks(): QueuedTask[] {
    return [...this.queue];
  }

  /**
   * 获取所有正在运行的任务
   */
  getAllRunningTasks(): ExtractionTask[] {
    return Array.from(this.runningTasks.values());
  }

  /**
   * 获取所有已完成的任务
   */
  getAllCompletedTasks(): ExtractionTask[] {
    return Array.from(this.completedTasks.values());
  }

  /**
   * 获取所有失败的任务
   */
  getAllFailedTasks(): ExtractionTask[] {
    return Array.from(this.failedTasks.values());
  }

  /**
   * 重新排列队列任务优先级
   */
  reorderQueue(taskId: string, newPriority: number): boolean {
    const index = this.queue.findIndex(item => item.task.id === taskId);
    if (index === -1) {
      return false;
    }

    // 移除任务
    const [queuedTask] = this.queue.splice(index, 1);
    queuedTask.priority = newPriority;

    // 重新插入到正确位置
    const insertIndex = this.queue.findIndex(item => item.priority > newPriority);
    if (insertIndex === -1) {
      this.queue.push(queuedTask);
    } else {
      this.queue.splice(insertIndex, 0, queuedTask);
    }

    extractorLogger.info(`🔄 任务优先级已更新: ${queuedTask.task.fileName} (新优先级: ${newPriority})`);
    this.emit("task-reordered", taskId, newPriority);
    return true;
  }

  /**
   * 设置最大并发数
   */
  setMaxConcurrent(maxConcurrent: number): void {
    this.maxConcurrent = Math.max(1, maxConcurrent);
    extractorLogger.info(`⚙️ 最大并发数已设置为: ${this.maxConcurrent}`);
    
    // 如果增加了并发数，尝试处理更多任务
    if (this.runningTasks.size < this.maxConcurrent) {
      this.processQueue();
    }
  }

  /**
   * 检查队列是否为空
   */
  isEmpty(): boolean {
    return this.queue.length === 0 && this.runningTasks.size === 0;
  }

  /**
   * 检查是否有正在运行的任务
   */
  hasRunningTasks(): boolean {
    return this.runningTasks.size > 0;
  }

  /**
   * 获取下一个要处理的任务
   */
  getNextTask(): QueuedTask | undefined {
    return this.queue[0];
  }

  /**
   * 清理已完成和失败的任务历史
   */
  clearHistory(): void {
    const completedCount = this.completedTasks.size;
    const failedCount = this.failedTasks.size;
    
    this.completedTasks.clear();
    this.failedTasks.clear();
    
    extractorLogger.info(`🧹 已清理任务历史: ${completedCount} 个已完成, ${failedCount} 个失败`);
    this.emit("history-cleared", { completedCount, failedCount });
  }
}
