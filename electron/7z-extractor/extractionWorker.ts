import { createLogger } from "../logger/index";

// 创建解压缩专用日志记录器
const extractorLogger = createLogger("extractor");
import { Worker } from "worker_threads";
import * as path from "path";
import { EventEmitter } from "events";
import type { ExtractionTask } from "./types";

export interface WorkerMessage {
  type: "start" | "pause" | "resume" | "cancel" | "progress" | "completed" | "error";
  taskId: string;
  data?: any;
}

export interface WorkerTask {
  taskId: string;
  archivePath: string;
  extractPath: string;
  password?: string;
  overwrite?: boolean;
}

export class ExtractionWorkerManager extends EventEmitter {
  private workers: Map<string, Worker> = new Map();
  private taskWorkerMap: Map<string, string> = new Map();
  private maxWorkers: number;
  private workerScript: string;

  constructor(maxWorkers: number = 2) {
    super();
    this.maxWorkers = maxWorkers;
    this.workerScript = path.join(__dirname, "worker.js");
  }

  /**
   * 开始解压缩任务
   */
  async startExtraction(task: ExtractionTask): Promise<void> {
    const workerId = this.getAvailableWorker();
    if (!workerId) {
      throw new Error("没有可用的Worker线程");
    }

    const worker = this.workers.get(workerId);
    if (!worker) {
      throw new Error("Worker线程不存在");
    }

    this.taskWorkerMap.set(task.id, workerId);

    const workerTask: WorkerTask = {
      taskId: task.id,
      archivePath: task.archivePath,
      extractPath: task.extractPath,
      password: task.password,
      overwrite: true, // 默认覆盖
    };

    worker.postMessage({
      type: "start",
      taskId: task.id,
      data: workerTask,
    } as WorkerMessage);
  }

  /**
   * 暂停解压缩任务
   */
  async pauseExtraction(taskId: string): Promise<void> {
    const workerId = this.taskWorkerMap.get(taskId);
    if (!workerId) {
      throw new Error("任务不存在或未在运行");
    }

    const worker = this.workers.get(workerId);
    if (worker) {
      worker.postMessage({
        type: "pause",
        taskId,
      } as WorkerMessage);
    }
  }

  /**
   * 恢复解压缩任务
   */
  async resumeExtraction(taskId: string): Promise<void> {
    const workerId = this.taskWorkerMap.get(taskId);
    if (!workerId) {
      throw new Error("任务不存在或未在运行");
    }

    const worker = this.workers.get(workerId);
    if (worker) {
      worker.postMessage({
        type: "resume",
        taskId,
      } as WorkerMessage);
    }
  }

  /**
   * 取消解压缩任务
   */
  async cancelExtraction(taskId: string): Promise<void> {
    const workerId = this.taskWorkerMap.get(taskId);
    if (!workerId) {
      return; // 任务可能已经完成或不存在
    }

    const worker = this.workers.get(workerId);
    if (worker) {
      worker.postMessage({
        type: "cancel",
        taskId,
      } as WorkerMessage);
    }

    // 清理任务映射
    this.taskWorkerMap.delete(taskId);
  }

  /**
   * 获取可用的Worker
   */
  private getAvailableWorker(): string | null {
    // 如果还没有达到最大Worker数量，创建新的Worker
    if (this.workers.size < this.maxWorkers) {
      const workerId = `worker-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      this.createWorker(workerId);
      return workerId;
    }

    // 查找空闲的Worker
    for (const [workerId, _worker] of this.workers) {
      const isIdle = !Array.from(this.taskWorkerMap.values()).includes(workerId);
      if (isIdle) {
        return workerId;
      }
    }

    return null; // 没有可用的Worker
  }

  /**
   * 创建Worker线程
   */
  private createWorker(workerId: string): void {
    try {
      const worker = new Worker(this.workerScript);

      // 设置消息监听器
      worker.on("message", (message: WorkerMessage) => {
        this.handleWorkerMessage(workerId, message);
      });

      // 设置错误监听器
      worker.on("error", (error: Error) => {
        extractorLogger.error(`Worker ${workerId} 错误:`, error);
        this.handleWorkerError(workerId, error);
      });

      // 设置退出监听器
      worker.on("exit", (code: number) => {
        extractorLogger.info(`Worker ${workerId} 退出，代码: ${code}`);
        this.cleanupWorker(workerId);
      });

      this.workers.set(workerId, worker);
      extractorLogger.info(`创建Worker线程: ${workerId}`);
    } catch (error) {
      extractorLogger.error(`创建Worker线程失败: ${workerId}`, error);
      throw error;
    }
  }

  /**
   * 处理Worker消息
   */
  private handleWorkerMessage(_workerId: string, message: WorkerMessage): void {
    const { type, taskId, data } = message;

    switch (type) {
      case "progress":
        this.emit("task-progress", taskId, data.progress, data.extractedSize, data.totalSize);
        break;

      case "completed":
        this.emit("task-completed", taskId, data.extractPath);
        this.taskWorkerMap.delete(taskId);
        break;

      case "error":
        this.emit("task-error", taskId, data.error);
        this.taskWorkerMap.delete(taskId);
        break;

      default:
        extractorLogger.warn(`未知的Worker消息类型: ${type}`);
    }
  }

  /**
   * 处理Worker错误
   */
  private handleWorkerError(workerId: string, error: Error): void {
    // 查找使用此Worker的任务
    const taskId = Array.from(this.taskWorkerMap.entries()).find(([_, wId]) => wId === workerId)?.[0];

    if (taskId) {
      this.emit("task-error", taskId, `Worker线程错误: ${error.message}`);
      this.taskWorkerMap.delete(taskId);
    }

    this.cleanupWorker(workerId);
  }

  /**
   * 清理Worker
   */
  private cleanupWorker(workerId: string): void {
    const worker = this.workers.get(workerId);
    if (worker) {
      try {
        worker.terminate();
      } catch (error) {
        extractorLogger.error(`终止Worker失败: ${workerId}`, error);
      }
      this.workers.delete(workerId);
    }

    // 清理相关的任务映射
    const tasksToClean = Array.from(this.taskWorkerMap.entries())
      .filter(([_, wId]) => wId === workerId)
      .map(([taskId]) => taskId);

    tasksToClean.forEach((taskId) => {
      this.taskWorkerMap.delete(taskId);
    });
  }

  /**
   * 关闭所有Worker
   */
  async shutdown(): Promise<void> {
    extractorLogger.info("关闭所有Worker线程...");

    const shutdownPromises = Array.from(this.workers.entries()).map(([_workerId, worker]) => {
      return new Promise<void>((resolve) => {
        worker.once("exit", () => resolve());
        worker.terminate();
      });
    });

    await Promise.all(shutdownPromises);

    this.workers.clear();
    this.taskWorkerMap.clear();
    extractorLogger.info("所有Worker线程已关闭");
  }

  /**
   * 获取活跃任务数量
   */
  getActiveTaskCount(): number {
    return this.taskWorkerMap.size;
  }

  /**
   * 获取Worker统计信息
   */
  getStats(): {
    totalWorkers: number;
    activeWorkers: number;
    activeTasks: number;
  } {
    const activeWorkers = new Set(this.taskWorkerMap.values()).size;

    return {
      totalWorkers: this.workers.size,
      activeWorkers,
      activeTasks: this.taskWorkerMap.size,
    };
  }
}
