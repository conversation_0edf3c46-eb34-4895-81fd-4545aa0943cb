import { BrowserWindow } from "electron";
import { StreamDownloadManager } from "./downloadManager";
import { registerDownloadIpcHandlers } from "./ipcHandlers";
import type { StreamDownloadConfig } from "./types";
import type { SevenZipExtractionManager } from "../7z-extractor/extractionManager";

// 导出类型
export type {
  DownloadTask,
  StreamDownloadConfig,
  DownloadStatus,
  DownloadEvents,
  DownloadApiResponse,
  DownloadStoreData,
  BatchDownloadTask,
  DownloadHistoryTask,
  BatchDownloadHistoryTask,
} from "./types";

// 导出 API 和监听器接口
export type { DownloadPreloadApi, DownloadEventListeners, SimplifiedDownloadPreloadApi } from "./preloadApi";

// 导出 API 创建函数
export { createDownloadApi, createDownloadEventListeners, createSimplifiedDownloadPreloadApi } from "./preloadApi";

// 导出下载管理器
export { StreamDownloadManager } from "./downloadManager";

/**
 * 初始化下载模块
 */
export function initializeDownloadModule(
  mainWindow: BrowserWindow,
  config: StreamDownloadConfig,
  extractionManager?: SevenZipExtractionManager
): {
  downloadManager: StreamDownloadManager;
  cleanup: () => void;
} {
  // 动态导入 logger，避免在 preload 脚本中包含
  let downloadLogger: any;
  try {
    // 只在主进程中导入 logger
    if (process.type === "browser") {
      const { createLogger } = require("../logger/index");
      downloadLogger = createLogger("download");
    } else {
      // 在其他进程中使用 console 作为后备
      downloadLogger = {
        info: console.log,
        warn: console.warn,
        error: console.error,
        debug: console.debug,
      };
    }
  } catch (error) {
    // 后备方案：使用 console
    downloadLogger = {
      info: console.log,
      warn: console.warn,
      error: console.error,
      debug: console.debug,
    };
  }

  downloadLogger.info("🚀 初始化 StreamSaver 下载模块");

  // 创建下载管理器
  const downloadManager = new StreamDownloadManager(config, extractionManager);

  // 注册 IPC 处理器
  registerDownloadIpcHandlers(downloadManager);

  // 转发事件到渲染进程
  const forwardEventToRenderer = (eventName: string) => {
    return (...args: any[]) => {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send(eventName, ...args);
      }
    };
  };

  // 绑定事件转发
  downloadManager.on("task-created", forwardEventToRenderer("download-task-created"));
  downloadManager.on("task-progress", forwardEventToRenderer("download-task-progress"));
  downloadManager.on("task-status-changed", forwardEventToRenderer("download-task-status-changed"));
  downloadManager.on("task-completed", forwardEventToRenderer("download-task-completed"));
  downloadManager.on("task-error", forwardEventToRenderer("download-task-error"));

  // 清理函数
  const cleanup = () => {
    downloadLogger.info("🧹 清理下载模块资源");
    downloadManager.removeAllListeners();
  };

  downloadLogger.info("✅ StreamSaver 下载模块初始化完成");

  return {
    downloadManager,
    cleanup,
  };
}
