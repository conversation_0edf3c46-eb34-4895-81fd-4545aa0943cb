import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import electron from "vite-plugin-electron";
import renderer from "vite-plugin-electron-renderer";
import { resolve } from "path";
import { fileURLToPath, URL } from "url";

// 获取当前文件的目录路径（ES module 方式）
const __dirname = fileURLToPath(new URL(".", import.meta.url));

export default defineConfig(({ command }) => ({
  plugins: [
    vue(),
    electron([
      {
        entry: "electron/main.ts",
        onstart(options) {
          if (options.startup) {
            options.startup([".", "--no-sandbox"]);
          }
        },
        vite: {
          build: {
            sourcemap: command === "serve",
            minify: command === "build",
            outDir: "dist-electron",
            rollupOptions: {
              external: ["electron"],
            },
            watch: command === "serve" ? {} : null,
          },
        },
      },
      {
        entry: "electron/preload.ts",
        onstart(options) {
          if (command === "serve") {
            options.reload();
          }
        },
        vite: {
          build: {
            sourcemap: command === "serve",
            minify: command === "build",
            outDir: "dist-electron",
            rollupOptions: {
              external: ["electron", "electron-log", "fs", "path", "os"],
            },
            watch: command === "serve" ? {} : null,
          },
        },
      },
      {
        entry: "electron/7z-extractor/worker.ts",
        vite: {
          build: {
            sourcemap: command === "serve",
            minify: command === "build",
            outDir: "dist-electron",
            rollupOptions: {
              external: ["electron", "worker_threads", "child_process", "fs", "path", "os"],
              output: {
                entryFileNames: "worker.js",
              },
            },
            watch: command === "serve" ? {} : null,
          },
        },
      },
    ]),
    renderer(),
  ],
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
      "@electron": resolve(__dirname, "electron"),
    },
  },
  server: {
    port: 5173,
    host: "0.0.0.0",
    proxy: {
      "/api": {
        target: "http://*************:8000",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
    },
  },
  build: {
    outDir: "dist",
    emptyOutDir: true,
    target: "esnext",
    minify: false,
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["vue", "vue-router", "pinia"],
          ui: ["lucide-vue-next", "radix-vue"],
        },
      },
    },
  },
}));
